@extends('layouts.web')

@section('title', $tag->name . ' Interview Questions - Qualifyrs')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    {{ $tag->name }} Interview Questions
                </h1>
                @if($tag->description)
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        {{ $tag->description }}
                    </p>
                @endif
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('interview.index') }}" class="text-gray-700 hover:text-blue-600">
                        Interview Questions
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">{{ $tag->name }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <!-- Tag Info -->
        <div class="mb-8 text-center">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium text-white"
                  style="background-color: {{ $tag->color }}">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                {{ $questions->total() }} Questions Tagged with "{{ $tag->name }}"
            </span>
        </div>

        <!-- Questions List -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="space-y-6">
                @forelse($questions as $question)
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('interview.show', $question->id) }}" class="hover:text-blue-600">
                                        {{ $question->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3">{{ Str::limit($question->question, 200) }}</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $question->category->category }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($question->difficulty) }}
                                    </span>
                                    <span>{{ $question->views }} views</span>
                                    <span>{{ $question->created_at->diffForHumans() }}</span>
                                </div>

                                <!-- Question Tags -->
                                @if($question->tags && $question->tags->count() > 0)
                                    <div class="flex flex-wrap gap-2 mt-3">
                                        @foreach($question->tags as $questionTag)
                                            <a href="{{ route('interview.tag', $questionTag->slug) }}" 
                                               class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white hover:opacity-80 transition-opacity {{ $questionTag->id === $tag->id ? 'ring-2 ring-white ring-opacity-50' : '' }}"
                                               style="background-color: {{ $questionTag->color }}">
                                                {{ $questionTag->name }}
                                            </a>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        <div class="text-gray-500 text-lg">No questions found with tag "{{ $tag->name }}".</div>
                        <p class="text-gray-400 mt-2">Check back later for new questions.</p>
                        <a href="{{ route('interview.index') }}" class="mt-4 inline-flex items-center text-blue-600 hover:text-blue-500">
                            ← Back to all questions
                        </a>
                    </div>
                @endforelse
            </div>

            @if($questions->hasPages())
                <div class="mt-8">
                    {{ $questions->links() }}
                </div>
            @endif
        </div>

        <!-- Related Tags -->
        <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Related Tags</h2>
            <div class="flex flex-wrap gap-2">
                @php
                    $relatedTags = \App\Models\Tag::active()->where('id', '!=', $tag->id)->popular(10)->get();
                @endphp
                @foreach($relatedTags as $relatedTag)
                    <a href="{{ route('interview.tag', $relatedTag->slug) }}" 
                       class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white hover:opacity-80 transition-opacity"
                       style="background-color: {{ $relatedTag->color }}">
                        {{ $relatedTag->name }}
                        <span class="ml-1 text-xs opacity-75">({{ $relatedTag->usage_count }})</span>
                    </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
