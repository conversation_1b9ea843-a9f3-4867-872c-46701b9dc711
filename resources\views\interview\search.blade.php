@extends('layouts.web')

@section('title', 'Search Results for "' . $query . '" - Interview Questions')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    Search Results
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Results for "<strong>{{ $query }}</strong>"
                </p>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('interview.index') }}" class="text-gray-700 hover:text-blue-600">
                        Interview Questions
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Search Results</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <form action="{{ route('interview.search') }}" method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="q" value="{{ $query }}" 
                           placeholder="Search interview questions..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Search
                </button>
            </form>
        </div>

        <!-- Search Results Stats -->
        <div class="mb-8 text-center">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {{ $questions->total() }} Results Found
            </span>
        </div>

        <!-- Questions List -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="space-y-6">
                @forelse($questions as $question)
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('interview.show', $question->id) }}" class="hover:text-blue-600">
                                        {!! str_ireplace($query, '<mark class="bg-yellow-200">' . $query . '</mark>', e($question->title)) !!}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3">
                                    {!! str_ireplace($query, '<mark class="bg-yellow-200">' . $query . '</mark>', e(Str::limit($question->question, 200))) !!}
                                </p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $question->category->category }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($question->difficulty) }}
                                    </span>
                                    <span>{{ $question->views }} views</span>
                                    <span>{{ $question->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div class="text-gray-500 text-lg">No questions found for "{{ $query }}"</div>
                        <p class="text-gray-400 mt-2">Try searching with different keywords or browse our categories.</p>
                        <div class="mt-6 space-x-4">
                            <a href="{{ route('interview.index') }}" class="inline-flex items-center text-blue-600 hover:text-blue-500">
                                ← Back to all questions
                            </a>
                            <a href="{{ route('interview.index') }}" class="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                Browse Categories
                            </a>
                        </div>
                    </div>
                @endforelse
            </div>

            @if($questions->hasPages())
                <div class="mt-8">
                    {{ $questions->appends(['q' => $query])->links() }}
                </div>
            @endif
        </div>

        <!-- Search Tips -->
        @if($questions->count() === 0)
            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Search Tips</h2>
                <ul class="space-y-2 text-gray-600">
                    <li>• Try using different keywords or synonyms</li>
                    <li>• Check your spelling</li>
                    <li>• Use more general terms</li>
                    <li>• Browse our categories to find related questions</li>
                </ul>
            </div>
        @endif
    </div>
</div>
@endsection
