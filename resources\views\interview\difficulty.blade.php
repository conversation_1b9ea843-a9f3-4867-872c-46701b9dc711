@extends('layouts.web')

@section('title', ucfirst($difficulty) . ' Interview Questions - Qualifyrs')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    {{ ucfirst($difficulty) }} Interview Questions
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Practice {{ $difficulty }}-level interview questions to prepare for your next job interview.
                </p>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('interview.index') }}" class="text-gray-700 hover:text-blue-600">
                        Interview Questions
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">{{ ucfirst($difficulty) }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <!-- Difficulty Badge -->
        <div class="mb-8 text-center">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium 
                @if($difficulty === 'beginner') bg-green-100 text-green-800
                @elseif($difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                @elseif($difficulty === 'advanced') bg-orange-100 text-orange-800
                @else bg-red-100 text-red-800 @endif">
                {{ $questions->total() }} {{ ucfirst($difficulty) }} Questions Available
            </span>
        </div>

        <!-- Questions List -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="space-y-6">
                @forelse($questions as $question)
                    <div class="border-b border-gray-200 pb-6 last:border-b-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('interview.show', $question->id) }}" class="hover:text-blue-600">
                                        {{ $question->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-3">{{ Str::limit($question->question, 200) }}</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $question->category->category }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($question->difficulty === 'beginner') bg-green-100 text-green-800
                                        @elseif($question->difficulty === 'intermediate') bg-yellow-100 text-yellow-800
                                        @elseif($question->difficulty === 'advanced') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($question->difficulty) }}
                                    </span>
                                    <span>{{ $question->views }} views</span>
                                    <span>{{ $question->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg">No {{ $difficulty }} questions found.</div>
                        <p class="text-gray-400 mt-2">Check back later for new questions.</p>
                        <a href="{{ route('interview.index') }}" class="mt-4 inline-flex items-center text-blue-600 hover:text-blue-500">
                            ← Back to all questions
                        </a>
                    </div>
                @endforelse
            </div>

            @if($questions->hasPages())
                <div class="mt-8">
                    {{ $questions->links() }}
                </div>
            @endif
        </div>

        <!-- Other Difficulty Levels -->
        <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Other Difficulty Levels</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach(['beginner', 'intermediate', 'advanced', 'expert'] as $level)
                    @if($level !== $difficulty)
                        <a href="{{ route('interview.difficulty', $level) }}" 
                           class="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="font-semibold 
                                @if($level === 'beginner') text-green-600
                                @elseif($level === 'intermediate') text-yellow-600
                                @elseif($level === 'advanced') text-orange-600
                                @else text-red-600 @endif">
                                {{ ucfirst($level) }}
                            </div>
                        </a>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
